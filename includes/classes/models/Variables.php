<?php

namespace cBuilder\Classes\Database;

use c<PERSON>uilder\Classes\Vendor\DataBaseModel;

class Variables extends DataBaseModel {
	protected static $table_prefix = 'ccb_';

	/**
	 * Generate Table Name
	 */
	public static function _table() {
		global $wpdb;
		return $wpdb->prefix . 'ccb_variables';
	}

	/**
	 * Create Table
	 */
	public static function create_table() {
		global $wpdb;

		require_once ABSPATH . 'wp-admin/includes/upgrade.php';

		$table_name  = self::_table();
		$primary_key = self::$primary_key;

		$sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
			{$primary_key} INT UNSIGNED NOT NULL AUTO_INCREMENT,
			name VARCHAR(255) NOT NULL UNIQUE,
			value DECIMAL(10,2) NOT NULL DEFAULT 0,
			description TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY ({$primary_key}),
			UNIQUE KEY unique_name (name)
		) {$wpdb->get_charset_collate()};";

		maybe_create_table( $table_name, $sql );
	}

	/**
	 * Get all variables
	 */
	public static function get_all_variables() {
		global $wpdb;
		$table_name = self::_table();

		$results = $wpdb->get_results(
			"SELECT * FROM {$table_name} ORDER BY name ASC",
			ARRAY_A
		);

		return $results ? $results : array();
	}

	/**
	 * Get variable by name
	 */
	public static function get_variable_by_name( $name ) {
		global $wpdb;
		$table_name = self::_table();

		$result = $wpdb->get_row(
			$wpdb->prepare( "SELECT * FROM {$table_name} WHERE name = %s", $name ),
			ARRAY_A
		);

		return $result;
	}

	/**
	 * Create or update variable
	 */
	public static function save_variable( $data ) {
		global $wpdb;
		$table_name = self::_table();

		$name = sanitize_text_field( $data['name'] );
		$value = floatval( $data['value'] );
		$description = sanitize_textarea_field( $data['description'] ?? '' );

		// Check if variable exists
		$existing = self::get_variable_by_name( $name );

		if ( $existing ) {
			// Update existing variable
			$result = $wpdb->update(
				$table_name,
				array(
					'value' => $value,
					'description' => $description,
					'updated_at' => current_time( 'mysql' )
				),
				array( 'name' => $name ),
				array( '%f', '%s', '%s' ),
				array( '%s' )
			);

			return $result !== false ? $existing['id'] : false;
		} else {
			// Insert new variable
			$result = $wpdb->insert(
				$table_name,
				array(
					'name' => $name,
					'value' => $value,
					'description' => $description
				),
				array( '%s', '%f', '%s' )
			);

			// Log any database errors for debugging
			if ( $wpdb->last_error ) {
				error_log( 'Variables save error: ' . $wpdb->last_error );
				error_log( 'Table name: ' . $table_name );
			}

			return $result ? $wpdb->insert_id : false;
		}
	}

	/**
	 * Delete variable
	 */
	public static function delete_variable( $id ) {
		global $wpdb;
		$table_name = self::_table();

		$result = $wpdb->delete(
			$table_name,
			array( 'id' => intval( $id ) ),
			array( '%d' )
		);

		return $result !== false;
	}

	/**
	 * Get variables as key-value pairs for formula processing
	 */
	public static function get_variables_for_formula() {
		$variables = self::get_all_variables();
		$result = array();

		foreach ( $variables as $variable ) {
			$result[ $variable['name'] ] = $variable['value'];
		}

		return $result;
	}

	/**
	 * Validate variable name
	 */
	public static function validate_variable_name( $name ) {
		// Variable name should be alphanumeric with underscores, no spaces
		if ( ! preg_match( '/^[a-zA-Z_][a-zA-Z0-9_]*$/', $name ) ) {
			return false;
		}

		// Check for reserved words
		$reserved_words = array( 'if', 'else', 'for', 'while', 'function', 'var', 'let', 'const', 'return' );
		if ( in_array( strtolower( $name ), $reserved_words ) ) {
			return false;
		}

		return true;
	}
}
