<?php

namespace cBuilder\Classes;

use c<PERSON>uilder\Classes\Database\Variables;

class CCBVariables {

	/**
	 * Get all variables
	 */
	public static function get_variables() {
		check_ajax_referer( 'ccb_get_variables', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'You are not allowed to run this action', 'cost-calculator-builder' ) );
		}

		// Ensure table exists
		Variables::create_table();

		$variables = Variables::get_all_variables();

		wp_send_json_success( array(
			'variables' => $variables,
			'message' => __( 'Variables retrieved successfully', 'cost-calculator-builder' )
		) );
	}

	/**
	 * Save variable
	 */
	public static function save_variable() {
		check_ajax_referer( 'ccb_save_variable', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'You are not allowed to run this action', 'cost-calculator-builder' ) );
		}

		// Ensure table exists
		Variables::create_table();

		$data = array();
		if ( isset( $_POST['name'] ) ) {
			$data['name'] = sanitize_text_field( $_POST['name'] );
		}
		if ( isset( $_POST['value'] ) ) {
			$data['value'] = floatval( $_POST['value'] );
		}
		if ( isset( $_POST['description'] ) ) {
			$data['description'] = sanitize_textarea_field( $_POST['description'] );
		}

		// Validate required fields
		if ( empty( $data['name'] ) ) {
			wp_send_json_error( __( 'Variable name is required', 'cost-calculator-builder' ) );
		}

		// Validate variable name format
		if ( ! Variables::validate_variable_name( $data['name'] ) ) {
			wp_send_json_error( __( 'Invalid variable name. Use only letters, numbers, and underscores. Must start with a letter or underscore.', 'cost-calculator-builder' ) );
		}

		$result = Variables::save_variable( $data );

		if ( $result ) {
			$variables = Variables::get_all_variables();
			wp_send_json_success( array(
				'variables' => $variables,
				'message' => __( 'Variable saved successfully', 'cost-calculator-builder' )
			) );
		} else {
			global $wpdb;
			$error_message = $wpdb->last_error ? $wpdb->last_error : __( 'Failed to save variable', 'cost-calculator-builder' );
			wp_send_json_error( $error_message );
		}
	}

	/**
	 * Delete variable
	 */
	public static function delete_variable() {
		check_ajax_referer( 'ccb_delete_variable', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'You are not allowed to run this action', 'cost-calculator-builder' ) );
		}

		$id = isset( $_POST['id'] ) ? intval( $_POST['id'] ) : 0;

		if ( ! $id ) {
			wp_send_json_error( __( 'Variable ID is required', 'cost-calculator-builder' ) );
		}

		$result = Variables::delete_variable( $id );

		if ( $result ) {
			$variables = Variables::get_all_variables();
			wp_send_json_success( array(
				'variables' => $variables,
				'message' => __( 'Variable deleted successfully', 'cost-calculator-builder' )
			) );
		} else {
			wp_send_json_error( __( 'Failed to delete variable', 'cost-calculator-builder' ) );
		}
	}

	/**
	 * Get variables for formula editor
	 */
	public static function get_variables_for_formula() {
		check_ajax_referer( 'ccb_get_variables_for_formula', 'nonce' );

		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'You are not allowed to run this action', 'cost-calculator-builder' ) );
		}

		$variables = Variables::get_variables_for_formula();

		wp_send_json_success( array(
			'variables' => $variables,
			'message' => __( 'Variables retrieved successfully', 'cost-calculator-builder' )
		) );
	}

	/**
	 * Process formula with variables
	 * This method replaces variable placeholders in formulas with actual values
	 */
	public static function process_formula_variables( $formula ) {
		if ( empty( $formula ) ) {
			return $formula;
		}

		$variables = Variables::get_variables_for_formula();

		// Replace variable syntax $variable_name with actual values
		foreach ( $variables as $name => $value ) {
			$pattern = '/\$' . preg_quote( $name, '/' ) . '\b/';
			$formula = preg_replace( $pattern, $value, $formula );
		}

		return $formula;
	}

	/**
	 * Get available variables for frontend and admin
	 */
	public static function get_available_variables() {
		$variables = Variables::get_all_variables();
		$result = array();

		foreach ( $variables as $variable ) {
			$result[] = array(
				'id' => $variable['id'],
				'name' => $variable['name'],
				'value' => $variable['value'],
				'description' => $variable['description'],
				'syntax' => '$' . $variable['name']
			);
		}

		return $result;
	}

	/**
	 * Debug AJAX function to check table status
	 */
	public static function debug_table_status() {
		if ( ! current_user_can( 'manage_options' ) ) {
			wp_send_json_error( __( 'You are not allowed to run this action', 'cost-calculator-builder' ) );
		}

		global $wpdb;

		$table_name = Variables::_table();

		// Check if table exists
		$table_exists = $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) );

		$debug_info = array(
			'table_name' => $table_name,
			'table_exists' => $table_exists ? 'Yes' : 'No',
			'wpdb_prefix' => $wpdb->prefix,
		);

		if ( $table_exists ) {
			// Get table structure
			$columns = $wpdb->get_results( "DESCRIBE {$table_name}", ARRAY_A );
			$debug_info['columns'] = $columns;

			// Try to count rows
			$count = $wpdb->get_var( "SELECT COUNT(*) FROM {$table_name}" );
			$debug_info['row_count'] = $count;

			if ( $wpdb->last_error ) {
				$debug_info['last_error'] = $wpdb->last_error;
			}
		} else {
			// Try to create the table
			Variables::create_table();
			$table_exists_after = $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) );
			$debug_info['table_created'] = $table_exists_after ? 'Yes' : 'No';
			if ( $wpdb->last_error ) {
				$debug_info['creation_error'] = $wpdb->last_error;
			}
		}

		wp_send_json_success( $debug_info );
	}
}
