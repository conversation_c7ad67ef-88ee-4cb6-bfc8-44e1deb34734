<div class="cbb-edit-field-container" v-if="open">
	<div class="ccb-edit-field-header">
		<span class="ccb-edit-field-title ccb-heading-3 ccb-bold"><?php esc_html_e( 'Formula', 'cost-calculator-builder' ); ?></span>
		<div class="ccb-field-actions">
			<button class="ccb-button default" @click="$emit( 'cancel' )"><?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?></button>
			<button class="ccb-button success" @click.prevent="save"><?php esc_html_e( 'Save', 'cost-calculator-builder' ); ?></button>
		</div>
	</div>
	<div class="ccb-grid-box">
		<div class="container">
			<div class="row">
				<div class="col-12">
					<div class="ccb-edit-field-switch">
						<div class="ccb-edit-field-switch-item ccb-default-title" :class="{active: tab === 'element'}" @click="tab = 'element'">
							<?php esc_html_e( 'Element', 'cost-calculator-builder' ); ?>
						</div>
						<div class="ccb-edit-field-switch-item ccb-default-title" :class="{active: tab === 'settings'}" @click="tab = 'settings'">
							<?php esc_html_e( 'Settings', 'cost-calculator-builder' ); ?>
							<span class="ccb-fields-required" v-if="errorsCount > 0">{{ errorsCount }}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="container" v-show="tab === 'element'">
			<div class="row ccb-p-t-20">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Title', 'cost-calculator-builder' ); ?></span>
						<input type="text" class="ccb-heading-5 ccb-light" v-model.trim="totalField.label" placeholder="<?php esc_attr_e( 'Enter field name', 'cost-calculator-builder' ); ?>">
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15" v-if="errorMessage.length > 0">
				<div class="col-12">
					<div class="ccb-formula-message-errors">
						<p class="ccb-formula-error-message" v-for="(item) in errorMessage">
							{{ item.message }}
						</p>
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15" id="ccb-variables-section">
				<div class="col-12">
					<div class="ccb-variables-helper">
						<h6 class="ccb-heading-6"><?php esc_html_e( 'Available Variables', 'cost-calculator-builder' ); ?></h6>
						<div class="ccb-variables-list" id="ccb-variables-list">
							<div id="ccb-variables-loading" style="color: #666; font-style: italic;">
								Loading variables...
							</div>
						</div>
						<small class="ccb-variables-description">
							<?php esc_html_e( 'Click on a variable to insert it into the formula. Variables are replaced with their values during calculation.', 'cost-calculator-builder' ); ?>
							<br>
							<a href="<?php echo admin_url( 'admin.php?page=cost_calculator_builder&tab=variables' ); ?>" target="_blank" style="color: #0073aa;">
								<?php esc_html_e( 'Manage Variables', 'cost-calculator-builder' ); ?>
							</a>
						</small>
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15" v-show="totalField.formulaView">
				<div class="col-12">
					<formula-view :field="field" @change="changeLegacy" v-model="totalField.legacyFormula" @error="setErrors" :id="totalField._id" :available_fields="available_fields"/>
				</div>
			</div>
			<div class="row ccb-p-t-10" v-show="!totalField.formulaView">
				<div class="col-12">
					<formula-field :field="field" @change="change" @error="setErrors" :id="totalField._id" v-model="totalField.costCalcFormula" :available_fields="available_fields" :formula_view="totalField.formulaView"/>
				</div>
			</div>
		</div>

		<div class="container" v-show="tab === 'settings'">
			<div class="row ccb-p-t-10">
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.formulaView" />
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Show the legacy formula view ', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10" v-if="!disableFieldHiddenByDefault(totalField)">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.hidden"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Hidden by Default', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.advancedJsCalculation"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Advanced calculations', 'cost-calculator-builder' ); ?></h6>
						<span class="ccb-options-tooltip">
							<i class="ccb-icon-circle-question"></i>
							<span class="ccb-options-tooltip__text"><?php esc_html_e( 'Enable for advanced calculations using JavaScript-based formulas. With over 9 totals, performance may slow. Disable for faster basic calculations.' ); ?></span>
						</span>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.calculateHidden"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Calculate hidden by default', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.fieldCurrency"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Add a measuring unit', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
			</div>
			<div class="row row-currency" :class="{'disabled': !totalField.fieldCurrency}">
				<div class="col-4">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Unit Symbol', 'cost-calculator-builder' ); ?></span>
						<input type="text" maxlength="18" v-model="fieldCurrency.currency" placeholder="<?php esc_attr_e( 'Enter unit symbol', 'cost-calculator-builder' ); ?>">
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-select-box">
						<span class="ccb-select-label"><?php esc_html_e( 'Position', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-select-wrapper">
							<i class="ccb-icon-Path-3485 ccb-select-arrow"></i>
							<select class="ccb-select" v-model="fieldCurrency.currencyPosition">
								<option value="left"><?php esc_html_e( 'Left', 'cost-calculator-builder' ); ?></option>
								<option value="right"><?php esc_html_e( 'Right', 'cost-calculator-builder' ); ?></option>
								<option value="left_with_space"><?php esc_html_e( 'Left with space', 'cost-calculator-builder' ); ?></option>
								<option value="right_with_space"><?php esc_html_e( 'Right with space', 'cost-calculator-builder' ); ?></option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-select-box">
						<span class="ccb-select-label"><?php esc_html_e( 'Thousands separator', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-select-wrapper">
							<i class="ccb-icon-Path-3485 ccb-select-arrow"></i>
							<select class="ccb-select" v-model="fieldCurrency.thousands_separator">
								<option value=","><?php esc_html_e( ' Comma ', 'cost-calculator-builder' ); ?></option>
								<option value="."><?php esc_html_e( ' Dot ', 'cost-calculator-builder' ); ?></option>
								<option value="'"><?php esc_html_e( ' Apostrophe ', 'cost-calculator-builder' ); ?></option>
								<option value=" "><?php esc_html_e( ' Space ', 'cost-calculator-builder' ); ?></option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-input-wrapper number">
						<span class="ccb-input-label"><?php esc_html_e( 'Number of decimals', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-input-box">
							<input type="number" name="num_after_integer" v-model="fieldCurrency.num_after_integer" min="0" max="8" placeholder="<?php esc_attr_e( 'Enter decimals', 'cost-calculator-builder' ); ?>" @blur="handleBlur">
							<span class="input-number-counter up" @click="numberCounterAction('num_after_integer')"></span>
							<span class="input-number-counter down" @click="numberCounterAction('num_after_integer', '-')"></span>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-select-box">
						<span class="ccb-select-label"><?php esc_html_e( 'Decimal separator', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-select-wrapper">
							<i class="ccb-icon-Path-3485 ccb-select-arrow"></i>
							<select class="ccb-select" v-model="fieldCurrency.decimal_separator">
								<option value=","><?php esc_html_e( ' Comma ', 'cost-calculator-builder' ); ?></option>
								<option value="."><?php esc_html_e( ' Dot ', 'cost-calculator-builder' ); ?></option>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Additional Classes', 'cost-calculator-builder' ); ?></span>
						<textarea class="ccb-heading-5 ccb-light" v-model="totalField.additionalStyles" placeholder="<?php esc_attr_e( 'Set Additional Classes', 'cost-calculator-builder' ); ?>"></textarea>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
.ccb-variables-helper {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 15px;
	margin-bottom: 15px;
}

.ccb-variables-helper h6 {
	margin: 0 0 10px 0;
	color: #495057;
	font-weight: 600;
}

.ccb-variables-list {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-bottom: 10px;
}

.ccb-variable-tag {
	background: #007cba;
	color: white;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	font-family: monospace;
	cursor: pointer;
	transition: background-color 0.2s;
}

.ccb-variable-tag:hover {
	background: #005a87;
}

.ccb-variables-description {
	color: #6c757d;
	font-size: 12px;
	line-height: 1.4;
}
</style>

<script>
// Variables functionality for total field
document.addEventListener('DOMContentLoaded', function() {
	// Populate variables list
	const populateVariablesList = () => {
		const variablesList = document.getElementById('ccb-variables-list');
		const variablesSection = document.getElementById('ccb-variables-section');

		// Debug logging
		console.log('CCB Variables Debug:', {
			variablesList: !!variablesList,
			variablesSection: !!variablesSection,
			ajax_window: !!window.ajax_window,
			variables: window.ajax_window ? window.ajax_window.variables : 'ajax_window not found',
			ajax_window_keys: window.ajax_window ? Object.keys(window.ajax_window) : 'no ajax_window'
		});

		// Show what's actually in ajax_window
		if (window.ajax_window) {
			console.log('Full ajax_window object:', window.ajax_window);
			console.log('Variables in ajax_window:', window.ajax_window.variables);
			console.log('Variables type:', typeof window.ajax_window.variables);
			console.log('Variables length:', Array.isArray(window.ajax_window.variables) ? window.ajax_window.variables.length : 'not array');
		}

		if (variablesList && window.ajax_window && window.ajax_window.variables) {
			const variables = window.ajax_window.variables;
			console.log('Variables found:', variables);

			if (variables.length > 0) {
				variablesList.innerHTML = '';
				variables.forEach(variable => {
					const span = document.createElement('span');
					span.className = 'ccb-variable-tag';
					span.textContent = '$' + variable.name + ' (' + variable.value + ')';
					span.onclick = () => window.insertVariable('$' + variable.name);
					variablesList.appendChild(span);
				});
				variablesSection.style.display = 'block';
				console.log('Variables populated successfully');
			} else {
				variablesList.innerHTML = '<div style="color: #999; font-style: italic;">No variables found. <a href="' +
					(window.ajax_window ? window.ajax_window.site_url : '') +
					'/wp-admin/admin.php?page=cost_calculator_builder&tab=variables" target="_blank">Create variables</a> to use in formulas.</div>';
				variablesSection.style.display = 'block';
				console.log('No variables found - showing message');
			}
		} else {
			// Try to load variables via AJAX if not available in ajax_window
			if (variablesList && window.ajax_window && window.ajax_window.ajax_url) {
				console.log('Attempting to load variables via AJAX...');

				fetch(window.ajax_window.ajax_url, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/x-www-form-urlencoded',
					},
					body: 'action=ccb_get_variables&nonce=' + (window.ajax_window.nonces ? window.ajax_window.nonces.ccb_get_variables || '' : '')
				})
				.then(response => response.json())
				.then(data => {
					console.log('AJAX Variables Response:', data);
					if (data.success && data.data.variables) {
						const variables = data.data.variables;
						if (variables.length > 0) {
							variablesList.innerHTML = '';
							variables.forEach(variable => {
								const span = document.createElement('span');
								span.className = 'ccb-variable-tag';
								span.textContent = '$' + variable.name + ' (' + variable.value + ')';
								span.onclick = () => window.insertVariable('$' + variable.name);
								variablesList.appendChild(span);
							});
							variablesSection.style.display = 'block';
							console.log('Variables loaded via AJAX successfully');
						} else {
							variablesList.innerHTML = '<div style="color: #999; font-style: italic;">No variables found. <a href="' +
								(window.ajax_window ? window.ajax_window.site_url : '') +
								'/wp-admin/admin.php?page=cost_calculator_builder&tab=variables" target="_blank">Create variables</a> to use in formulas.</div>';
							variablesSection.style.display = 'block';
							console.log('No variables returned from AJAX');
						}
					} else {
						variablesSection.style.display = 'none';
						console.log('AJAX request failed or returned no variables');
					}
				})
				.catch(error => {
					console.error('Error loading variables via AJAX:', error);
					if (variablesSection) {
						variablesSection.style.display = 'none';
					}
				});
			} else {
				if (variablesSection) {
					variablesSection.style.display = 'none';
				}
				console.log('Variables section hidden - missing requirements');
			}
		} else {
			// Fallback: remove loading message and hide section
			if (variablesList) {
				variablesList.innerHTML = '<div style="color: #999; font-style: italic;">Variables not available. Check console for details.</div>';
			}
			if (variablesSection) {
				variablesSection.style.display = 'block';
			}
			console.log('Variables section fallback - no ajax_window or variables');
		}
	};

	// Wait for Vue to be available and add variables functionality
	const addVariablesFunctionality = () => {
		// Add getVariables computed property to Vue instances
		if (window.Vue && window.Vue.prototype) {
			// For Vue 2
			if (!window.Vue.prototype.$getVariables) {
				Object.defineProperty(window.Vue.prototype, '$getVariables', {
					get: function() {
						if (window.ajax_window && window.ajax_window.variables) {
							return window.ajax_window.variables.map(variable => ({
								name: variable.name,
								value: variable.value,
								syntax: '$' + variable.name,
								description: variable.description
							}));
						}
						return [];
					}
				});
			}





			if (!window.Vue.prototype.$insertVariable) {
				window.Vue.prototype.$insertVariable = function(syntax) {
					// Try to find the formula input field
					let formulaInput = null;

					// Look for various formula input selectors
					const selectors = [
						'textarea[placeholder*="formula"]',
						'input[placeholder*="formula"]',
						'.formula-view textarea',
						'.formula-field input',
						'textarea.calc-textarea',
						'input[type="text"].ccb-heading-5'
					];

					for (let selector of selectors) {
						formulaInput = document.querySelector(selector);
						if (formulaInput && formulaInput.offsetParent !== null) { // visible element
							break;
						}
					}

					// If we found a formula input, insert the variable
					if (formulaInput) {
						const start = formulaInput.selectionStart || 0;
						const end = formulaInput.selectionEnd || 0;
						const currentValue = formulaInput.value || '';

						const newValue = currentValue.substring(0, start) + syntax + currentValue.substring(end);

						// Update the input value
						formulaInput.value = newValue;

						// Trigger input event to update Vue model
						const event = new Event('input', { bubbles: true });
						formulaInput.dispatchEvent(event);

						// Set cursor position after the inserted variable
						setTimeout(() => {
							formulaInput.focus();
							formulaInput.setSelectionRange(start + syntax.length, start + syntax.length);
						}, 10);
					} else {
						// Fallback: copy to clipboard
						if (navigator.clipboard) {
							navigator.clipboard.writeText(syntax).then(() => {
								alert('Variable syntax copied to clipboard: ' + syntax);
							});
						} else {
							// Fallback for older browsers
							const textArea = document.createElement('textarea');
							textArea.value = syntax;
							document.body.appendChild(textArea);
							textArea.select();
							document.execCommand('copy');
							document.body.removeChild(textArea);
							alert('Variable syntax copied to clipboard: ' + syntax);
						}
					}
				};
			}
		}
	};

	// Try to add functionality immediately and also after a delay
	addVariablesFunctionality();
	populateVariablesList();
	setTimeout(() => {
		addVariablesFunctionality();
		populateVariablesList();
	}, 1000);
	setTimeout(() => {
		addVariablesFunctionality();
		populateVariablesList();
	}, 3000);
	setTimeout(() => {
		addVariablesFunctionality();
		populateVariablesList();
	}, 5000);
});

// Global functions for template access
window.getVariables = function() {
	if (window.ajax_window && window.ajax_window.variables) {
		return window.ajax_window.variables.map(variable => ({
			name: variable.name,
			value: variable.value,
			syntax: '$' + variable.name,
			description: variable.description
		}));
	}
	return [];
};

window.insertVariable = function(syntax) {
	if (window.Vue && window.Vue.prototype && window.Vue.prototype.$insertVariable) {
		window.Vue.prototype.$insertVariable(syntax);
	} else {
		// Direct fallback
		if (navigator.clipboard) {
			navigator.clipboard.writeText(syntax).then(() => {
				alert('Variable syntax copied to clipboard: ' + syntax);
			});
		}
	}
};




</script>
