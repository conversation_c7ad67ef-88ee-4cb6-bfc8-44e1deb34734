<?php
wp_enqueue_script( 'cbb-bundle-js', CALC_URL . '/frontend/dist/admin.js', array(), CALC_VERSION, true );
wp_enqueue_script( 'ccb-variables-js', CALC_URL . '/frontend/dist/variables.js', array( 'cbb-bundle-js' ), CALC_VERSION, true );
wp_localize_script(
	'cbb-bundle-js',
	'ajax_window',
	array(
		'ajax_url'      => admin_url( 'admin-ajax.php' ),
		'plugin_url'    => CALC_URL,
		'site_url'      => site_url(),
		'translations'  => array_merge( \cBuilder\Classes\CCBTranslations::get_frontend_translations(), \cBuilder\Classes\CCBTranslations::get_backend_translations() ),
		'pro_active'    => ccb_pro_active(),
	)
);
?>

<div class="ccb-settings-wrapper calculator-settings" id="ccb_variables_page">
	<variables-page inline-template>
		<div class="ccb-main-container">
			<div class="ccb-tab-content">
				<div class="ccb-tab-sections ccb-loader-section" v-if="loader">
					<loader></loader>
				</div>
				<template v-else>
					<div class="ccb-variables-page">
						<div class="ccb-variables-header">
							<div class="ccb-variables-title">
								<h2 class="ccb-heading-2"><?php esc_html_e( 'Variables', 'cost-calculator-builder' ); ?></h2>
								<p class="ccb-description"><?php esc_html_e( 'Create and manage global variables that can be used in calculator formulas. Variables are referenced using the syntax $variable_name in formulas.', 'cost-calculator-builder' ); ?></p>
							</div>
							<div class="ccb-variables-actions">
								<button class="ccb-button success" @click="showAddModal = true">
									<i class="ccb-icon-Path-3453"></i>
									<?php esc_html_e( 'Add Variable', 'cost-calculator-builder' ); ?>
								</button>
							</div>
						</div>

						<div class="ccb-variables-content">
							<div class="ccb-variables-table-wrapper" v-if="variables.length > 0">
								<table class="ccb-variables-table">
									<thead>
										<tr>
											<th><?php esc_html_e( 'Name', 'cost-calculator-builder' ); ?></th>
											<th><?php esc_html_e( 'Value', 'cost-calculator-builder' ); ?></th>
											<th><?php esc_html_e( 'Description', 'cost-calculator-builder' ); ?></th>
											<th><?php esc_html_e( 'Syntax', 'cost-calculator-builder' ); ?></th>
											<th><?php esc_html_e( 'Actions', 'cost-calculator-builder' ); ?></th>
										</tr>
									</thead>
									<tbody>
										<tr v-for="variable in variables" :key="variable.id">
											<td class="ccb-variable-name">{{ variable.name }}</td>
											<td class="ccb-variable-value">{{ variable.value }}</td>
											<td class="ccb-variable-description">{{ variable.description || '-' }}</td>
											<td class="ccb-variable-syntax">
												<code>${{ variable.name }}</code>
											</td>
											<td class="ccb-variable-actions">
												<button class="ccb-button default small" @click="editVariable(variable)">
													<i class="ccb-icon-Path-3503"></i>
													<?php esc_html_e( 'Edit', 'cost-calculator-builder' ); ?>
												</button>
												<button class="ccb-button danger small" @click="deleteVariable(variable.id)">
													<i class="ccb-icon-Path-3496"></i>
													<?php esc_html_e( 'Delete', 'cost-calculator-builder' ); ?>
												</button>
											</td>
										</tr>
									</tbody>
								</table>
							</div>

							<div class="ccb-variables-empty" v-else>
								<div class="ccb-empty-state">
									<i class="ccb-icon-Path-3453 ccb-empty-icon"></i>
									<h3 class="ccb-heading-3"><?php esc_html_e( 'No Variables Found', 'cost-calculator-builder' ); ?></h3>
									<p class="ccb-description"><?php esc_html_e( 'Create your first variable to use in calculator formulas.', 'cost-calculator-builder' ); ?></p>
									<button class="ccb-button success" @click="showAddModal = true">
										<?php esc_html_e( 'Add Variable', 'cost-calculator-builder' ); ?>
									</button>
								</div>
							</div>
						</div>

						<!-- Add/Edit Variable Modal -->
						<div class="ccb-modal-overlay" v-if="showAddModal || showEditModal" @click="closeModal">
							<div class="ccb-modal ccb-variable-modal" @click.stop>
								<div class="ccb-modal-header">
									<h3 class="ccb-heading-3">
										{{ showEditModal ? '<?php esc_html_e( 'Edit Variable', 'cost-calculator-builder' ); ?>' : '<?php esc_html_e( 'Add Variable', 'cost-calculator-builder' ); ?>' }}
									</h3>
									<button class="ccb-modal-close" @click="closeModal">
										<i class="ccb-icon-close"></i>
									</button>
								</div>
								<div class="ccb-modal-body">
									<div class="ccb-input-wrapper">
										<label class="ccb-input-label"><?php esc_html_e( 'Variable Name', 'cost-calculator-builder' ); ?> *</label>
										<input
											type="text"
											v-model="currentVariable.name"
											:disabled="showEditModal"
											placeholder="<?php esc_attr_e( 'e.g., tax_rate, base_price', 'cost-calculator-builder' ); ?>"
											class="ccb-input"
										>
										<small class="ccb-input-description">
											<?php esc_html_e( 'Use only letters, numbers, and underscores. Must start with a letter or underscore.', 'cost-calculator-builder' ); ?>
										</small>
									</div>
									<div class="ccb-input-wrapper">
										<label class="ccb-input-label"><?php esc_html_e( 'Value', 'cost-calculator-builder' ); ?> *</label>
										<input
											type="number"
											step="0.01"
											v-model="currentVariable.value"
											placeholder="<?php esc_attr_e( 'Enter numeric value', 'cost-calculator-builder' ); ?>"
											class="ccb-input"
										>
									</div>
									<div class="ccb-input-wrapper">
										<label class="ccb-input-label"><?php esc_html_e( 'Description', 'cost-calculator-builder' ); ?></label>
										<textarea
											v-model="currentVariable.description"
											placeholder="<?php esc_attr_e( 'Optional description for this variable', 'cost-calculator-builder' ); ?>"
											class="ccb-textarea"
											rows="3"
										></textarea>
									</div>
									<div class="ccb-variable-preview" v-if="currentVariable.name">
										<label class="ccb-input-label"><?php esc_html_e( 'Formula Syntax', 'cost-calculator-builder' ); ?></label>
										<code class="ccb-code-preview">${{ currentVariable.name }}</code>
									</div>
								</div>
								<div class="ccb-modal-footer">
									<button class="ccb-button default" @click="closeModal">
										<?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?>
									</button>
									<button class="ccb-button success" @click="saveVariable" :disabled="!isValidVariable">
										{{ showEditModal ? '<?php esc_html_e( 'Update', 'cost-calculator-builder' ); ?>' : '<?php esc_html_e( 'Add', 'cost-calculator-builder' ); ?>' }}
									</button>
								</div>
							</div>
						</div>

						<!-- Delete Confirmation Modal -->
						<div class="ccb-modal-overlay" v-if="showDeleteModal" @click="showDeleteModal = false">
							<div class="ccb-modal ccb-delete-modal" @click.stop>
								<div class="ccb-modal-header">
									<h3 class="ccb-heading-3"><?php esc_html_e( 'Delete Variable', 'cost-calculator-builder' ); ?></h3>
								</div>
								<div class="ccb-modal-body">
									<p><?php esc_html_e( 'Are you sure you want to delete this variable? This action cannot be undone and may affect calculators that use this variable.', 'cost-calculator-builder' ); ?></p>
								</div>
								<div class="ccb-modal-footer">
									<button class="ccb-button default" @click="showDeleteModal = false">
										<?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?>
									</button>
									<button class="ccb-button danger" @click="confirmDelete">
										<?php esc_html_e( 'Delete', 'cost-calculator-builder' ); ?>
									</button>
								</div>
							</div>
						</div>
					</div>
				</template>
			</div>
		</div>
	</variables-page>
</div>

<style>
.ccb-variables-page {
	padding: 20px;
}

.ccb-variables-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 30px;
}

.ccb-variables-title h2 {
	margin: 0 0 10px 0;
}

.ccb-variables-table-wrapper {
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
	overflow: hidden;
}

.ccb-variables-table {
	width: 100%;
	border-collapse: collapse;
}

.ccb-variables-table th,
.ccb-variables-table td {
	padding: 15px;
	text-align: left;
	border-bottom: 1px solid #eee;
}

.ccb-variables-table th {
	background: #f8f9fa;
	font-weight: 600;
}

.ccb-variable-syntax code {
	background: #f1f3f4;
	padding: 4px 8px;
	border-radius: 4px;
	font-family: monospace;
	color: #d73502;
}

.ccb-variable-actions {
	display: flex;
	gap: 8px;
}

.ccb-empty-state {
	text-align: center;
	padding: 60px 20px;
}

.ccb-empty-icon {
	font-size: 48px;
	color: #ccc;
	margin-bottom: 20px;
}

.ccb-variable-modal {
	width: 500px;
	max-width: 90vw;
}

.ccb-code-preview {
	display: block;
	background: #f1f3f4;
	padding: 10px;
	border-radius: 4px;
	font-family: monospace;
	color: #d73502;
	border: 1px solid #ddd;
}
</style>
